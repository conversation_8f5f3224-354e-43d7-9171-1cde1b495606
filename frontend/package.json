{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@heroicons/react": "^2.2.0", "@heroui/react": "^2.7.4", "axios": "^1.8.4", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "chart.js": "^4.4.9", "delaunay-fast": "^1.0.1", "dotenv": "^16.4.7", "framer-motion": "^12.4.7", "jsonwebtoken": "^9.0.2", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "lucide-react": "^0.477.0", "moment": "^2.30.1", "mysql2": "^3.12.0", "next": "^15.3.0-canary.25", "papaparse": "^5.5.2", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-loading-skeleton": "^3.5.0", "react-parallax-tilt": "^1.7.286", "react-select": "^5.10.1", "sass": "^1.86.0", "styled-components": "^6.1.16", "sweetalert2": "^11.17.2", "swr": "^2.3.3", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/jspdf": "^1.3.3", "eslint": "^9", "eslint-config-next": "15.1.7", "postcss": "^8", "tailwindcss": "^3.4.1"}}